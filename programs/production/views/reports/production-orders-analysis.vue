<template>
    <ui-view
        class="production-reports-production-orders-analysis"
        type="content"
        :left-panel-width="400"
        v-if="isInitialized"
    >
        <template slot="top-panel">
            <ui-scope
                id="production.reports.production-orders-analysis"
                :filters="scopeApplicableFilters"
                @changed="handleScopeChange"
            />
        </template>

        <template slot="left-panel">
            <ui-list
                ref="productList"
                collection="inventory.products"
                :extra-fields="['code', 'barcode', 'definition', 'image']"
                enable-search
                single-select
                style="background-color: white"
                :item-height="60"
                :filters="{canBeProduced: true}"
                :html-template="getProductsCellTemplate"
                @selected-items="handleSelect"
            />
        </template>

        <div class="order-items">
            <ui-table
                collection="production.orders"
                :process-result="processResult"
                :columns="columns"
                :filters="orderFilters"
                :extra-fields="['_id']"
                :summary-row="summaryRow"
            />
        </div>
    </ui-view>
</template>

<script>
import _ from 'lodash';
import fastCopy from 'fast-copy';
import {leadingZeros} from 'framework/helpers';

export default {
    data: () => ({
        isInitialized: false,
        scopeQuery: {},
        selected: null
    }),

    computed: {
        orderFilters() {
            const filters = fastCopy(this.scopeQuery);

            if (_.isPlainObject(this.selected)) {
                filters.productId = this.selected._id;
            }

            return filters;
        },
        scopeApplicableFilters() {
            const self = this;

            return [
                {code: 'today', label: 'Today', query: 'orderDate|today'},
                {
                    code: 'yesterday',
                    label: 'Yesterday',
                    query: 'orderDate|yesterday'
                },
                {
                    code: 'thisWeek',
                    label: 'This week',
                    query: 'orderDate|thisWeek'
                },
                {
                    code: 'lastWeek',
                    label: 'Last week',
                    query: 'orderDate|lastWeek'
                },
                {
                    code: 'thisMonth',
                    label: 'This month',
                    query: 'orderDate|thisMonth'
                },
                {
                    code: 'lastMonth',
                    label: 'Last month',
                    query: 'orderDate|lastMonth'
                },
                {
                    code: 'thisQuarter',
                    label: 'This quarter',
                    query: 'orderDate|thisQuarter'
                },
                {
                    code: 'lastQuarter',
                    label: 'Last quarter',
                    query: 'orderDate|lastQuarter'
                },

                {field: 'code', label: 'Code'},
                {
                    field: 'orderDate',
                    code: 'orderDate',
                    label: 'Order date',
                    type: 'date'
                },
                {
                    field: 'plannedStartDate',
                    label: 'Planned start date',
                    type: 'date'
                },
                {
                    field: 'plannedEndDate',
                    label: 'Planned end date',
                    type: 'date'
                },
                {
                    field: 'quantity',
                    label: 'Quantity',
                    type: 'decimal'
                },
                {
                    field: 'branchId',
                    label: 'Branch office',
                    collection: 'kernel.branches',
                    filters: {
                        _id: {$in: this.$user.branchIds},
                        $sort: {name: 1}
                    },
                    condition() {
                        return self.$setting('system.multiBranch');
                    }
                },
                {
                    field: 'warehouseId',
                    label: 'Warehouse',
                    collection: 'inventory.warehouses',
                    filters: {$sort: {name: 1}}
                },
                {
                    field: 'relatedPartnerId',
                    label: 'Related partner',
                    collection: 'kernel.partners'
                },
                {
                    field: 'progress',
                    label: 'Progress',
                    type: 'integer'
                },
                {
                    field: 'status',
                    label: 'Status',
                    translateLabels: true,
                    items: [
                        {value: 'draft', label: 'Draft'},
                        {value: 'planning', label: 'Planning'},
                        {value: 'producing', label: 'Producing'},
                        {value: 'completed', label: 'Completed'},
                        {value: 'canceled', label: 'Canceled'}
                    ]
                }
            ];
        },
        columns() {
            const self = this;

            return [
                {
                    field: 'code',
                    label: 'Code',
                    width: 120,
                    relationParams(params) {
                        const data = params.data;

                        return {
                            id: data._id,
                            view: 'production.production.orders'
                        };
                    }
                },
                {
                    field: 'orderDate',
                    label: 'Order Date',
                    format: 'date',
                    sort: 'desc',
                    width: 120
                },
                {
                    field: 'product.code',
                    label: 'Product code',
                    subSelect: ['code'],
                    relationParams(params) {
                        const data = params.data;
                        const relation = {};

                        relation.isVisible = _.isObject(data.product) && _.isString(data.product.code);

                        if (relation.isVisible) {
                            relation.view = 'inventory.catalog.products';
                            relation.id = data.productId;
                            relation.template = '{{code}}';
                        }

                        return relation;
                    },
                    width: 150
                },
                {
                    field: 'product.definition',
                    label: 'Product definition',
                    subSelect: ['definition'],
                    relationParams(params) {
                        const data = params.data;
                        const relation = {};

                        relation.isVisible = _.isObject(data.product) && _.isString(data.product.definition);

                        if (relation.isVisible) {
                            relation.view = 'inventory.catalog.products';
                            relation.id = data.productId;
                            relation.template = '{{definition}}';
                        }

                        return relation;
                    },
                    minWidth: 210
                },
                {
                    field: 'description',
                    label: 'Description',
                    width: 200
                },
                {
                    field: 'quantity',
                    label: 'Quantity',
                    width: 120,
                    format: 'unit',
                    formatOptions(row) {
                        return {number: {precision: self.$setting('system.unitPrecision')}};
                    }
                },
                {
                    field: 'unit.name',
                    label: 'Unit',
                    width: 90
                },
                {
                    field: 'branch.name',
                    label: 'Branch Office',
                    hidden: !this.$setting('system.multiBranch'),
                    width: 180,
                    relationParams(params) {
                        const data = params.data;

                        if (_.isPlainObject(data.branch) && _.isString(data.branch._id)) {
                            return {
                                id: data.branch._id,
                                view: 'system.management.configuration.branches'
                            };
                        }

                        return {};
                    }
                },
                {
                    field: 'warehouse.name',
                    label: 'Warehouse',
                    width: 180,
                    relationParams(params) {
                        const data = params.data;

                        if (_.isPlainObject(data.warehouse) && _.isString(data.warehouse._id)) {
                            return {
                                id: data.warehouse._id,
                                view: 'inventory.configuration.warehouses'
                            };
                        }

                        return {};
                    }
                },
                {
                    field: 'relatedPartner',
                    label: 'Related partner',
                    subSelect: ['code', 'name'],
                    relationParams(params) {
                        const data = params.data;

                        if (_.isPlainObject(data)) {
                            return {
                                view: 'partners.partners',
                                template: '{{code}} - {{name}}',
                                id: data.relatedPartnerId
                            };
                        }

                        return {};
                    },
                    width: 240
                },
                {
                    field: 'plannedStartDate',
                    label: 'Planned start date',
                    format: 'datetime',
                    width: 155
                },
                {
                    field: 'plannedEndDate',
                    label: 'Planned end date',
                    format: 'datetime',
                    width: 155
                },
                {
                    field: 'plannedCost',
                    label: 'Planned cost',
                    width: 140,
                    format: 'currency',
                    hidden: () =>
                        !this.$checkPermission({
                            type: 'permission',
                            name: 'system.canSeeCosts'
                        })
                },
                {
                    field: 'actualCost',
                    label: 'Actual cost',
                    width: 140,
                    format: 'currency',
                    hidden: () =>
                        !this.$checkPermission({
                            type: 'permission',
                            name: 'system.canSeeCosts'
                        })
                },
                {
                    field: 'costDifference',
                    label: 'Cost difference',
                    width: 120,
                    format: 'currency',
                    hidden: () =>
                        !this.$checkPermission({
                            type: 'permission',
                            name: 'system.canSeeCosts'
                        })
                },
                {
                    field: 'costDifferencePercentage',
                    label: 'Cost difference (%)',
                    width: 130,
                    format: 'percentage',
                    hidden: () =>
                        !this.$checkPermission({
                            type: 'permission',
                            name: 'system.canSeeCosts'
                        })
                },
                {
                    field: 'plannedDuration',
                    label: 'Planned duration',
                    width: 120,
                    render: params => {
                        if (!!params.data && _.isFinite(params.data.plannedDuration)) {
                            const now = this.$datetime.local();
                            const date = now.plus({minutes: params.data.plannedDuration});
                            const diff = date.diff(now, ['hours', 'minutes', 'seconds']);
                            const info = diff.toObject();
                            const hours = parseInt(info.hours, 10);
                            const minutes = parseInt(info.minutes, 10);
                            const seconds = parseInt(info.seconds, 10);

                            return `${leadingZeros(hours, 2)}:${leadingZeros(minutes, 2)}:${leadingZeros(seconds, 2)}`;
                        }

                        return '';
                    }
                },
                {
                    field: 'actualDuration',
                    label: 'Actual duration',
                    width: 120,
                    render: params => {
                        if (!!params.data && _.isFinite(params.data.actualDuration)) {
                            const now = this.$datetime.local();
                            const date = now.plus({minutes: params.data.actualDuration});
                            const diff = date.diff(now, ['hours', 'minutes', 'seconds']);
                            const info = diff.toObject();
                            const hours = parseInt(info.hours, 10);
                            const minutes = parseInt(info.minutes, 10);
                            const seconds = parseInt(info.seconds, 10);

                            return `${leadingZeros(hours, 2)}:${leadingZeros(minutes, 2)}:${leadingZeros(seconds, 2)}`;
                        }

                        return '';
                    }
                },
                {
                    field: 'plannedQuantity',
                    label: 'Planned quantity',
                    width: 150,
                    format: 'unit'
                },
                {
                    field: 'scrapQuantity',
                    label: 'Scrap quantity',
                    width: 150,
                    format: 'unit'
                },
                {
                    field: 'completedQuantity',
                    label: 'Completed quantity',
                    width: 150,
                    format: 'unit'
                },
                {
                    field: 'progress',
                    label: 'Progress',
                    width: 210,
                    progressCell: true
                },
                {
                    field: 'status',
                    label: 'Status',
                    tagsCell: true,
                    translateLabels: true,
                    tagLabels: [
                        {value: 'draft', label: 'Draft', color: 'default'},
                        {value: 'planning', label: 'Planning', color: 'teal'},
                        {value: 'producing', label: 'Producing', color: 'success'},
                        {value: 'completed', label: 'Completed', color: 'primary'},
                        {value: 'canceled', label: 'Canceled', color: 'danger'}
                    ],
                    width: 150
                }
            ];
        }
    },

    methods: {
        getProductsCellTemplate(item) {
            const imageUrl = item.image
                ? this.$app.absoluteUrl(`files/${item.image}`)
                : this.$app.absoluteUrl('static/images/no-image.png');

            return `
            <div class="producable-product-cell">
                <div class="product-cell-container">
                    <img class="product-cell-image" src="${imageUrl}" />
                </div>
                <div class="product-cell-content">
                    <div class="product-cell-definition">${item.definition}</div>
                    <div class="product-cell-code">
                        ${item.code}
                    </div>
                </div>
            </div>
            `.trim();
        },
        async handleScopeChange(model) {
            this.scopeQuery = model.query;
        },
        async handleSelect(selected) {
            this.selected = selected = selected[0];
        },
        async processResult(result) {
            const orderIds = result.data.map(item => item._id);

            const orderItems = await this.$collection('production.order-items').aggregate([
                {
                    $match: {
                        orderId: {$in: orderIds}
                    }
                },
                {
                    $group: {
                        _id: {orderId: '$orderId', status: '$status'},
                        count: {$sum: 1}
                    }
                },
                {
                    $group: {
                        _id: '$_id.orderId',
                        statuses: {
                            $push: {
                                status: '$_id.status',
                                count: '$count'
                            }
                        }
                    }
                }
            ]);
            const orderItemMap = _.keyBy(orderItems, '_id');

            const tasks = await this.$collection('production.tasks').aggregate([
                {
                    $match: {
                        orderId: {$in: orderIds}
                    }
                },
                {
                    $group: {
                        _id: '$orderId',
                        plannedCost: {$sum: '$plannedCost'},
                        actualCost: {$sum: '$actualCost'},
                        plannedDuration: {$sum: '$plannedDuration'},
                        actualDuration: {$sum: '$actualDuration'}
                    }
                },
                {
                    $project: {
                        plannedCost: 1,
                        actualCost: 1,
                        plannedDuration: 1,
                        actualDuration: 1
                    }
                }
            ]);
            const taskMap = _.keyBy(tasks, '_id');

            result.data = result.data.map(row => {
                const task = taskMap[row._id] || {};
                const orderItem = orderItemMap[row._id] || {};

                row.completedQuantity =
                    ((orderItem.statuses || []).find(s => s.status === 'produced') || {}).count || 0;
                row.plannedQuantity = _.isNumber(row.quantity) ? row.quantity : 0;
                row.scrapQuantity = ((orderItem.statuses || []).find(s => s.status === 'scrap') || {}).count || 0;

                row.plannedCost = _.isNumber(task.plannedCost) ? task.plannedCost : 0;
                row.actualCost = _.isNumber(task.actualCost) ? task.actualCost : 0;
                row.plannedDuration = _.isNumber(task.plannedDuration) ? task.plannedDuration : 0;
                row.actualDuration = _.isNumber(task.actualDuration) ? task.actualDuration : 0;

                row.costDifference = Math.abs(row.actualCost > 0 ? row.actualCost - row.plannedCost : 0);
                row.costDifferencePercentage = Math.abs(
                    row.plannedCost > 0 && row.actualCost > 0
                        ? ((row.actualCost - row.plannedCost) / row.plannedCost) * 100
                        : 0
                );

                return row;
            });

            return result;
        },
        async summaryRow(rows) {
            const totals = {
                quantity: 0,
                plannedCost: 0,
                actualCost: 0,
                costDifference: 0,
                plannedQuantity: 0,
                scrapQuantity: 0,
                completedQuantity: 0,
                plannedDuration: 0,
                actualDuration: 0
            };

            let dataToSum = rows;
            if (!dataToSum || dataToSum.length === 0) {
                const result = await this.$collection('production.orders').find(this.orderFilters);
                const processedResult = await this.processResult({data: result, total: result.length});
                dataToSum = processedResult.data;
            }

            dataToSum.forEach(row => {
                totals.quantity += _.isNumber(row.quantity) ? row.quantity : 0;
                totals.plannedCost += _.isNumber(row.plannedCost) ? row.plannedCost : 0;
                totals.actualCost += _.isNumber(row.actualCost) ? row.actualCost : 0;
                totals.costDifference += _.isNumber(row.costDifference) ? row.costDifference : 0;
                totals.plannedQuantity += _.isNumber(row.plannedQuantity) ? row.plannedQuantity : 0;
                totals.scrapQuantity += _.isNumber(row.scrapQuantity) ? row.scrapQuantity : 0;
                totals.completedQuantity += _.isNumber(row.completedQuantity) ? row.completedQuantity : 0;
                totals.plannedDuration += _.isNumber(row.plannedDuration) ? row.plannedDuration : 0;
                totals.actualDuration += _.isNumber(row.actualDuration) ? row.actualDuration : 0;
            });

            totals.costDifferencePercentage = totals.plannedCost > 0 && totals.actualCost > 0
                ? Math.abs(((totals.actualCost - totals.plannedCost) / totals.plannedCost) * 100)
                : 0;

            return {
                code: this.$t('TOTAL'),
                quantity: totals.quantity,
                plannedCost: totals.plannedCost,
                actualCost: totals.actualCost,
                costDifference: totals.costDifference,
                costDifferencePercentage: totals.costDifferencePercentage,
                plannedQuantity: totals.plannedQuantity,
                scrapQuantity: totals.scrapQuantity,
                completedQuantity: totals.completedQuantity,
                plannedDuration: totals.plannedDuration,
                actualDuration: totals.actualDuration
            };
        }
    },

    async created() {
        this.isInitialized = true;
    }
};
</script>

<style lang="scss">
//noinspection CssUnknownTarget
@import 'core';

.production-reports-production-orders-analysis {
    .ui-content {
        display: flex;
        flex-flow: column nowrap;
    }

    .order-items {
        flex: 0 0 100%;
    }
}

.producable-product-cell {
    display: flex;
    padding: 10px;

    .product-cell-container {
        display: flex;
        flex-flow: column nowrap;
        justify-content: center;
        align-items: center;
        width: 40px;
        height: 40px;
    }

    .product-cell-image {
        display: flex;
        flex-flow: column nowrap;
        justify-content: center;
        align-items: center;
        border-radius: 6px;
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
    }

    .product-cell-content {
        display: flex;
        flex-flow: column;
        justify-content: center;
        flex: 1;
        margin-left: 10px;
        min-width: 0;
    }

    .product-cell-definition {
        width: 100%;
        overflow: hidden;
        font-weight: 700;
        @include text-truncate();
        font-size: 14px;
        line-height: 1;
        margin-bottom: 10px;
    }

    .product-cell-code {
        width: 100%;
        overflow: hidden;
        @include text-truncate();
        font-size: 12px;
        color: #7f878a;
        line-height: 1;
    }
}
</style>
