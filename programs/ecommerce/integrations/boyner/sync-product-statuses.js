import _ from 'lodash';
import {apiRequest} from './utils';

export default async function (app, store, onProgress) {
    console.log('ULANNNNN')
    let integrationParams = null;
    try {
        integrationParams = JSON.parse(store.integrationParams);
    } catch (error) {
        console.error(error);

        return;
    }
    const {url, supplierId, apiKey, apiSecret} = integrationParams;

    let page = 0;
    let currentCount = 0;
    let totalCount = 0;

    await (async function iterator() {
        const start = app.datetime.local().minus({days: 1}).startOf('day').toJSDate().getTime();
        const end = app.datetime.local().endOf('day').toJSDate().getTime();
        const result = await apiRequest({
            url,
            path: `sapigw/suppliers/${supplierId}/products?startDate=${start}&endDate=${end}&dateQueryType=LAST_MODIFIED_DATE&page=${page}&size=50`,
            supplierId,
            apiKey,
            apiSecret
        });
        totalCount = result.totalElements;

        if (Array.isArray(result.content) && result.content.length > 0) {
            const storeProductOperations = [];

            for (const item of result.content) {
                const barcode = (item.barcode ?? '').toString().trim();
                if (!barcode) continue;

                const storeProduct = await app.collection('ecommerce.store-products').findOne({
                    storeId: store._id,
                    productBarcode: barcode,
                    $select: ['_id']
                });
                if (!storeProduct) continue;

                const sp = {};
                sp.integrationId = item.id;
                sp.integrationStatus = 'waiting';
                if (item.approved === true) {
                    sp.integrationStatus = 'approved';
                }
                if (item.rejected === true) {
                    sp.integrationStatus = 'rejected';

                    sp.integrationRejectionReason = [];
                    for (const reason of item.rejectReasonDetails ?? []) {
                        sp.integrationRejectionReason.push(
                            `${reason.rejectReason ?? ''}\n${reason.rejectReasonDetail ?? ''}`
                        );
                    }
                    sp.integrationRejectionReason = sp.integrationRejectionReason.join('\n\n');
                } else {
                    sp.integrationRejectionReason = '';
                }
                if (item.blacklisted === true) {
                    sp.integrationStatus = 'blacklisted';
                }

                storeProductOperations.push({
                    updateOne: {
                        filter: {_id: storeProduct._id},
                        update: {$set: sp}
                    }
                });
            }

            if (storeProductOperations.length > 0) {
                await app.collection('ecommerce.store-products').bulkWrite(storeProductOperations);
            }

            page++;
            currentCount += result.content.length;

            await iterator();
        }
    })();
}
